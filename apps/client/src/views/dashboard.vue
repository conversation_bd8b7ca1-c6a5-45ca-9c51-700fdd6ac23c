<script setup lang="ts">
import {
  ArcElement,
  CategoryScale,
  Chart as ChartJS,
  Legend,
  LinearScale,
  LineElement,
  PointElement,
  Title,
  Tooltip,
} from 'chart.js'
import {
  BarChart3,
  DollarSign,
  FileText,
  HelpCircle,
  Key,
  Layers,
  Plus,
  RefreshCw,
  Zap,
} from 'lucide-vue-next'
import { computed, onMounted, reactive, ref } from 'vue'
import {
  Doughnut,
  Line,
} from 'vue-chartjs'
import { toast } from 'vue-sonner'
import { appApi, billingApi } from '@/api'
import ClientLayout from '@/layouts/ClientLayout.vue'
import { useUserStore } from '@/store'

// 注册Chart.js组件
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
)

const userStore = useUserStore()
const loading = ref(false)
const trendPeriod = ref('7d')

// 统计数据
const stats = reactive({
  monthlyUsage: 0,
  monthlyGrowth: 0,
  monthlyCost: 0,
  costGrowth: 0,
  applicationCount: 0,
  apiKeyCount: 0,
  activeApiKeys: 0,
})

// 趋势数据
const trendData = reactive({
  labels: [],
  datasets: [],
})

// 服务分布数据
const serviceData = reactive({
  labels: [],
  datasets: [],
})

// 最近应用
const recentApplications = ref([])

// 图表配置
const trendChartData = computed(() => ({
  labels: trendData.labels,
  datasets: [
    {
      label: 'Token使用量',
      data: trendData.datasets[0]?.data || [],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4,
    },
  ],
}))

const trendChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
} as any

const serviceChartData = computed(() => ({
  labels: serviceData.labels,
  datasets: [
    {
      data: serviceData.datasets[0]?.data || [],
      backgroundColor: [
        'rgba(59, 130, 246, 0.8)',
        'rgba(16, 185, 129, 0.8)',
        'rgba(139, 92, 246, 0.8)',
        'rgba(245, 158, 11, 0.8)',
      ],
      borderWidth: 0,
    },
  ],
}))

const serviceChartOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
} as any

// 格式化数字
function formatNumber(num: number) {
  if (num >= 1000000) {
    return `${(num / 1000000).toFixed(1)}M`
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`
  }
  return num.toString()
}

// 格式化货币
function formatCurrency(amount: number) {
  return amount.toFixed(2)
}

// 加载统计数据
async function loadStats() {
  try {
    const data = await billingApi.getBillingStats()
    Object.assign(stats, data)
  }
  catch (error) {
    console.error('Failed to load stats:', error)
  }
}

// 加载趋势数据
async function loadTrendData() {
  try {
    const data = await billingApi.getUsageTrends({ period: trendPeriod.value as 'day' | 'week' | 'month' | 'custom' })
    Object.assign(trendData, data)
  }
  catch (error) {
    console.error('Failed to load trend data:', error)
  }
}

// 加载服务分布数据
async function loadServiceData() {
  try {
    const data = await billingApi.getServiceUsage({ period: 'month' })
    Object.assign(serviceData, data)
  }
  catch (error) {
    console.error('Failed to load service data:', error)
  }
}

// 加载最近应用
async function loadRecentApplications() {
  try {
    const data = await appApi.getList({
      page_size: 5,
    })
    recentApplications.value = Array.isArray(data) ? data.slice(0, 5) : []
  }
  catch (error) {
    console.error('Failed to load recent applications:', error)
  }
}

// 刷新数据
async function refreshData() {
  loading.value = true
  try {
    await Promise.all([
      loadStats(),
      loadTrendData(),
      loadServiceData(),
      loadRecentApplications(),
    ])
    toast.success('数据已刷新')
  }
  catch (error) {
    toast.error('刷新失败，请重试')
  }
  finally {
    loading.value = false
  }
}

// 初始化数据
onMounted(() => {
  refreshData()
})
</script>

<template>
  <ClientLayout>
    <div class="space-y-6">
      <!-- 页面标题 -->
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            控制台
          </h1>
          <p class="text-gray-600">
            欢迎回来，{{ userStore.user?.name || '用户' }}！
          </p>
        </div>
        <div class="flex items-center space-x-3">
          <button
            :disabled="loading"
            class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            @click="refreshData"
          >
            <RefreshCw
              class="w-4 h-4 mr-2"
              :class="[{ 'animate-spin': loading }]"
            />
            刷新
          </button>
          <RouterLink
            to="/applications/create"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <Plus class="w-4 h-4 mr-2" />
            创建应用
          </RouterLink>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <Zap class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    本月使用量
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ formatNumber(stats.monthlyUsage) }} Tokens
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <span class="text-green-600 font-medium">
                +{{ stats.monthlyGrowth }}%
              </span>
              <span class="text-gray-500">较上月</span>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <DollarSign class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    本月费用
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    ¥{{ formatCurrency(stats.monthlyCost) }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <span class="text-red-600 font-medium">
                +{{ stats.costGrowth }}%
              </span>
              <span class="text-gray-500">较上月</span>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <Layers class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    应用数量
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ stats.applicationCount }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <RouterLink
                to="/applications"
                class="text-blue-600 hover:text-blue-500 font-medium"
              >
                查看全部
              </RouterLink>
            </div>
          </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
          <div class="p-5">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                  <Key class="w-5 h-5 text-white" />
                </div>
              </div>
              <div class="ml-5 w-0 flex-1">
                <dl>
                  <dt class="text-sm font-medium text-gray-500 truncate">
                    API Keys
                  </dt>
                  <dd class="text-lg font-medium text-gray-900">
                    {{ stats.apiKeyCount }}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-5 py-3">
            <div class="text-sm">
              <span class="text-green-600 font-medium">
                {{ stats.activeApiKeys }}
              </span>
              <span class="text-gray-500">个活跃</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 图表区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 使用趋势图 -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              使用趋势
            </h3>
            <select
              v-model="trendPeriod"
              class="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
              @change="loadTrendData"
            >
              <option value="7d">
                最近7天
              </option>
              <option value="30d">
                最近30天
              </option>
              <option value="90d">
                最近90天
              </option>
            </select>
          </div>
          <div class="h-64">
            <Line
              v-if="trendData.labels.length > 0"
              :data="trendChartData"
              :options="trendChartOptions"
            />
            <div
              v-else
              class="flex items-center justify-center h-full text-gray-500"
            >
              暂无数据
            </div>
          </div>
        </div>

        <!-- 服务分布图 -->
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900">
              服务使用分布
            </h3>
          </div>
          <div class="h-64">
            <Doughnut
              v-if="serviceData.labels.length > 0"
              :data="serviceChartData"
              :options="serviceChartOptions"
            />
            <div
              v-else
              class="flex items-center justify-center h-full text-gray-500"
            >
              暂无数据
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- 最近应用 -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              最近应用
            </h3>
          </div>
          <div class="divide-y divide-gray-200">
            <div
              v-for="app in recentApplications"
              :key="app.id"
              class="px-6 py-4 hover:bg-gray-50"
            >
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Layers class="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <p class="text-sm font-medium text-gray-900">
                      {{ app.name }}
                    </p>
                    <p class="text-xs text-gray-500">
                      {{ app.description }}
                    </p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-sm text-gray-900">
                    {{ formatNumber(app.usage) }}
                  </p>
                  <p class="text-xs text-gray-500">
                    今日使用
                  </p>
                </div>
              </div>
            </div>
            <div
              v-if="recentApplications.length === 0"
              class="px-6 py-8 text-center"
            >
              <Layers class="mx-auto h-12 w-12 text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">
                暂无应用
              </h3>
              <p class="mt-1 text-sm text-gray-500">
                创建您的第一个应用开始使用
              </p>
              <div class="mt-6">
                <RouterLink
                  to="/applications/create"
                  class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  <Plus class="w-4 h-4 mr-2" />
                  创建应用
                </RouterLink>
              </div>
            </div>
          </div>
        </div>

        <!-- 快速操作 -->
        <div class="bg-white shadow rounded-lg">
          <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">
              快速操作
            </h3>
          </div>
          <div class="p-6">
            <div class="grid grid-cols-2 gap-4">
              <RouterLink
                to="/applications/create"
                class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-blue-50 text-blue-700 group-hover:bg-blue-100">
                    <Plus class="w-6 h-6" />
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-lg font-medium text-gray-900">
                    创建应用
                  </h3>
                  <p class="mt-2 text-sm text-gray-500">
                    创建新的应用并获取API Key
                  </p>
                </div>
              </RouterLink>

              <RouterLink
                to="/billing"
                class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-green-50 text-green-700 group-hover:bg-green-100">
                    <BarChart3 class="w-6 h-6" />
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-lg font-medium text-gray-900">
                    查看账单
                  </h3>
                  <p class="mt-2 text-sm text-gray-500">
                    查看详细的使用统计和费用
                  </p>
                </div>
              </RouterLink>

              <a
                href="#"
                class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-purple-50 text-purple-700 group-hover:bg-purple-100">
                    <FileText class="w-6 h-6" />
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-lg font-medium text-gray-900">
                    API文档
                  </h3>
                  <p class="mt-2 text-sm text-gray-500">
                    查看API接口文档和示例
                  </p>
                </div>
              </a>

              <a
                href="#"
                class="group relative bg-white p-6 focus-within:ring-2 focus-within:ring-inset focus-within:ring-blue-500 border border-gray-200 rounded-lg hover:border-gray-300 transition-colors"
              >
                <div>
                  <span class="rounded-lg inline-flex p-3 bg-orange-50 text-orange-700 group-hover:bg-orange-100">
                    <HelpCircle class="w-6 h-6" />
                  </span>
                </div>
                <div class="mt-4">
                  <h3 class="text-lg font-medium text-gray-900">
                    帮助支持
                  </h3>
                  <p class="mt-2 text-sm text-gray-500">
                    获取技术支持和帮助文档
                  </p>
                </div>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ClientLayout>
</template>
