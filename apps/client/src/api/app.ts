import type {
  Api<PERSON><PERSON>,
  PaginationParams,
} from '@billing/common'
import { http } from '@uozi-admin/request'

export interface CreateAppRequest extends Pick<
  ApiKey,
  'name'
  | 'comment'
> { }

export interface UpdateAppRequest extends Partial<Pick<ApiKey, 'name' | 'comment'>> { }

export const appApi = {
  // 获取应用列表
  getList: (params: PaginationParams): Promise<ApiKey[]> => {
    return http.get('/client/apps', { params })
  },

  // 获取应用详情
  getApplication: (id: number): Promise<ApiKey> => {
    return http.get(`/client/apps/${id}`)
  },

  // 创建应用（自动生成API Key）
  createApplication: (data: CreateAppRequest): Promise<ApiKey> => {
    return http.post('/client/apps', data)
  },

  // 更新应用
  updateApplication: (id: number, data: UpdateAppRequest): Promise<ApiKey> => {
    return http.put(`/client/apps/${id}`, data)
  },

  // 删除应用
  deleteApplication: (id: number): Promise<void> => {
    return http.delete(`/client/apps/${id}`)
  },

  // 重新生成API Key
  regenerateApiKey: (applicationId: number): Promise<ApiKey> => {
    return http.post(`/client/apps/${applicationId}/regenerate`)
  },
}
