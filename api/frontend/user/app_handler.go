package user

import (
	"net/http"
	"strconv"

	"git.uozi.org/uozi/potato-billing-api/api"
	"git.uozi.org/uozi/potato-billing-api/model"
	"git.uozi.org/uozi/potato-billing-api/query"
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/uozi-tech/cosy"
)

// CreateApplicationRequest 创建应用请求
type CreateApplicationRequest struct {
	Name    string `json:"name" binding:"required"`
	Comment string `json:"comment"`
}

// UpdateApplicationRequest 更新应用请求
type UpdateApplicationRequest struct {
	Name    string `json:"name"`
	Comment string `json:"comment"`
}

// GetApplicationList 获取应用列表
func GetApplicationList(c *gin.Context) {
	user := api.CurrentUser(c)

	core := cosy.Core[model.ApiKey](c)
	core.SetPreloads("User")
	core.SetEqual("user_id")

	// 设置当前用户ID过滤
	c.Set("user_id", user.ID)

	core.PagingList()
}

// GetApplication 获取应用详情
func GetApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	apiKey, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.ID.Eq(id), query.ApiKey.UserID.Eq(user.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, apiKey)
}

// CreateApplication 创建应用
func CreateApplication(c *gin.Context) {
	user := api.CurrentUser(c)

	var req CreateApplicationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 创建新的API Key记录作为应用
	apiKey := &model.ApiKey{
		APIKey:  uuid.New().String(),
		Name:    req.Name,
		Status:  "ok",
		UserID:  user.ID,
		Comment: req.Comment,
	}

	err := query.ApiKey.Create(apiKey)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 重新查询以获取完整信息
	result, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.ID.Eq(apiKey.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// UpdateApplication 更新应用
func UpdateApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	var req UpdateApplicationRequest
	if bindErr := c.ShouldBindJSON(&req); bindErr != nil {
		cosy.ErrHandler(c, bindErr)
		return
	}

	// 检查应用是否属于当前用户
	_, err = query.ApiKey.
		Where(query.ApiKey.ID.Eq(id), query.ApiKey.UserID.Eq(user.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 更新应用信息
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.Comment != "" {
		updates["comment"] = req.Comment
	}

	_, err = query.ApiKey.
		Where(query.ApiKey.ID.Eq(id), query.ApiKey.UserID.Eq(user.ID)).
		Updates(updates)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 返回更新后的数据
	result, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.ID.Eq(id)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}

// DeleteApplication 删除应用
func DeleteApplication(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 检查应用是否属于当前用户
	_, err = query.ApiKey.
		Where(query.ApiKey.ID.Eq(id), query.ApiKey.UserID.Eq(user.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 删除应用
	_, err = query.ApiKey.
		Where(query.ApiKey.ID.Eq(id), query.ApiKey.UserID.Eq(user.ID)).
		Delete()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "应用删除成功"})
}

// RegenerateApiKey 重新生成API Key
func RegenerateApiKey(c *gin.Context) {
	user := api.CurrentUser(c)
	idStr := c.Param("id")

	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 检查应用是否属于当前用户
	_, err = query.ApiKey.
		Where(query.ApiKey.ID.Eq(id), query.ApiKey.UserID.Eq(user.ID)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 生成新的API Key
	newApiKey := uuid.New().String()

	_, err = query.ApiKey.
		Where(query.ApiKey.ID.Eq(id), query.ApiKey.UserID.Eq(user.ID)).
		Update(query.ApiKey.APIKey, newApiKey)
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	// 返回更新后的数据
	result, err := query.ApiKey.
		Preload(query.ApiKey.User).
		Where(query.ApiKey.ID.Eq(id)).
		First()
	if err != nil {
		cosy.ErrHandler(c, err)
		return
	}

	c.JSON(http.StatusOK, result)
}
